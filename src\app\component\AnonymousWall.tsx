"use client";
import React, { useState, useEffect, useRef } from "react";
import { Smile, Send, Lock, Trash2 } from "lucide-react";
import { Client, Databases, ID } from "appwrite";
import dynamic from "next/dynamic";
import type { EmojiClickData } from "emoji-picker-react";
import gsap from "gsap";
import { Draggable } from "gsap/Draggable";

// Register the Draggable plugin
gsap.registerPlugin(Draggable);

const EmojiPicker = dynamic(() => import("emoji-picker-react"), { ssr: false });

// Environment variables
const DATABASE_ID = process.env.NEXT_PUBLIC_DB_ID || "";
const COLLECTION_ID = process.env.NEXT_PUBLIC_COLLECTION_ID || "";
const PROJECT_ID = process.env.NEXT_PUBLIC_PROJECT_ID || "";
const ENDPOINT =
  process.env.NEXT_PUBLIC_ENDPOINT || "https://cloud.appwrite.io/v1";
const ADMIN_PASSWORD = process.env.NEXT_PUBLIC_PASSWORD || "";

// Appwrite setup
const client = new Client().setEndpoint(ENDPOINT).setProject(PROJECT_ID);
const databases = new Databases(client);

interface WallMessage {
  id: string;
  text: string;
  timestamp: number;
  color: string;
  border: string;
  shadow: string;
  gradient: string;
  opacity: string;
  position: { x: number; y: number };
  isAdmin: boolean;
  width?: number;
  height?: number;
}

// Enhanced color palette with brighter, more vibrant gradients (reverted to original)
const colors = [
  {
    gradient: "from-emerald-500 to-emerald-700",
    border: "border-emerald-500",
    shadow: "shadow-emerald-500",
    bgGradient: "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-purple-500 to-indigo-700",
    border: "border-purple-500",
    shadow: "shadow-purple-500",
    bgGradient: "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-rose-500 to-red-700",
    border: "border-rose-500",
    shadow: "shadow-rose-500",
    bgGradient: "bg-gradient-to-br from-rose-500/80 to-red-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-cyan-500 to-teal-700",
    border: "border-cyan-500",
    shadow: "shadow-cyan-500",
    bgGradient: "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
    opacity: "opacity-95",
  },
  {
    gradient: "from-amber-500 to-orange-700",
    border: "border-amber-500",
    shadow: "shadow-amber-500",
    bgGradient: "bg-gradient-to-br from-amber-500/80 to-orange-700/80",
    opacity: "opacity-95",
  },
];

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp);
  const now = new Date();
  const isToday = date.toDateString() === now.toDateString();

  // Format time
  const time = date
    .toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    })
    .toLowerCase();

  // If today, show "Today at [time]", otherwise show date and time
  if (isToday) {
    return `Today at ${time}`;
  } else {
    const dateStr = date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    return `${dateStr} at ${time}`;
  }
};

// Draggable message component
const DraggableMessage = ({
  message,
  updateMessagePosition,
  boardId = "mobile-board", // Default to mobile-board
}: {
  message: WallMessage;
  updateMessagePosition: (
    id: string,
    position: { x: number; y: number }
  ) => void;
  boardId?: string;
}) => {
  const rotationRef = useRef(Math.random() * 4 - 2);
  const messageRef = useRef<HTMLDivElement>(null);
  const draggableRef = useRef<Draggable | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Ensure position is valid (not negative or too large)
  const safePosition = {
    x: Math.max(0, message.position.x || 0),
    y: Math.max(0, message.position.y || 0),
  };

  // Initialize GSAP Draggable with enhanced smoothness and accuracy
  useEffect(() => {
    if (!messageRef.current) return;

    // Clean up previous instance if it exists
    if (draggableRef.current) {
      draggableRef.current.kill();
    }

    // Create Draggable instance with premium smoothness and accuracy
    draggableRef.current = Draggable.create(messageRef.current, {
      type: "x,y",
      bounds: `#${boardId}`, // Use the provided board ID for bounds
      edgeResistance: 0.65, // Smooth edge resistance for natural feel
      // Advanced smoothness settings
      dragClickables: true,
      minimumMovement: 1, // Slight threshold to prevent accidental drags
      allowNativeTouchScrolling: false, // Prevent conflicts on mobile
      allowEventDefault: false,
      // Cursor settings for better UX
      cursor: "grabbing",
      activeCursor: "grabbing",
      // Performance optimizations
      force3D: true, // Hardware acceleration
      // Enhanced momentum with speed-responsive inertia
      inertia: {
        resistance: 250, // Reduced resistance for longer momentum
        minDuration: 0.4, // Minimum momentum duration
        maxDuration: 1.8, // Extended maximum for high-speed throws
        velocityScale: 1.2, // Amplify velocity for more dramatic momentum
      },
      // Enhanced callbacks for ultra-smooth animations
      onPress: function () {
        // Immediate visual feedback with smooth pickup animation
        gsap.killTweensOf(messageRef.current);
        gsap.set(messageRef.current, {
          transformOrigin: "center center",
          willChange: "transform, box-shadow, filter",
        });

        // Smooth pickup effect with elastic ease
        gsap.to(messageRef.current, {
          scale: 1.03,
          rotation: rotationRef.current * 0.3, // Subtle rotation hint
          boxShadow: "0 12px 35px rgba(0,0,0,0.18)",
          filter: "brightness(1.02) saturate(1.05)",
          duration: 0.25,
          ease: "power2.out",
          zIndex: 1000,
        });
      },
      onDragStart: function () {
        setIsDragging(true);

        // Enhanced floating effect with smooth transitions
        gsap.to(messageRef.current, {
          scale: 1.06,
          rotation: rotationRef.current * 0.5,
          boxShadow: "0 18px 45px rgba(0,0,0,0.25)",
          filter: "brightness(1.05) saturate(1.1)",
          duration: 0.3,
          ease: "power1.out",
        });
      },
      onDrag: function () {
        // Enhanced floating effect with speed-based animations
        if (this.isDragging) {
          // Calculate velocity with higher sensitivity for dramatic effects
          const velocity = Math.min(
            Math.abs(this.deltaX) + Math.abs(this.deltaY),
            100
          );
          const normalizedVelocity = velocity / 100; // 0 to 1

          // Dynamic scaling based on speed - more dramatic at high speeds
          const speedScale = 1.06 + normalizedVelocity * 0.08; // 1.06 to 1.14

          // Enhanced shadow effects for high-speed floating
          const shadowIntensity = 0.25 + normalizedVelocity * 0.15;
          const shadowBlur = 18 + velocity * 0.8;
          const shadowSpread = 15 + velocity * 0.6;

          // Rotation effect based on drag direction and speed
          const rotationOffset =
            this.deltaX * 0.02 + normalizedVelocity * rotationRef.current * 0.3;

          // Motion blur and brightness effects for high-speed dragging
          const motionBlur = velocity * 0.015;
          const brightness = 1.05 + normalizedVelocity * 0.05;
          const saturation = 1.1 + normalizedVelocity * 0.1;

          gsap.to(messageRef.current, {
            scale: speedScale,
            rotation: rotationRef.current * 0.5 + rotationOffset,
            boxShadow: `0 ${shadowBlur}px ${shadowSpread}px rgba(0,0,0,${shadowIntensity})`,
            filter: `brightness(${brightness}) saturate(${saturation}) blur(${motionBlur}px)`,
            duration: 0.08, // Faster response for high-speed effects
            ease: "none",
            overwrite: "auto",
          });
        }
      },
      onDragEnd: function () {
        setIsDragging(false);

        // Calculate final drag velocity for speed-based settling
        const finalVelocity = Math.min(
          Math.abs(this.deltaX) + Math.abs(this.deltaY),
          100
        );
        const normalizedVelocity = finalVelocity / 100; // 0 to 1

        // Get precise final position using GSAP's built-in properties
        const finalX = this.x;
        const finalY = this.y;

        // Speed-based settling animation - faster drops settle quicker, slower drops are more gentle
        const settleDuration = 0.3 + normalizedVelocity * 0.4; // 0.3s to 0.7s based on speed
        const settleEase =
          normalizedVelocity > 0.3 ? "power4.out" : "power3.out"; // More dramatic easing for high speeds

        // Add slight overshoot effect for high-speed drops
        const overshootScale = normalizedVelocity > 0.5 ? 0.98 : 1; // Slight compression for high-speed drops

        // Enhanced drop animation with speed-based effects and admin priority
        gsap.to(messageRef.current, {
          scale: overshootScale,
          rotation:
            rotationRef.current +
            normalizedVelocity * rotationRef.current * 0.2, // Extra rotation for high speeds
          boxShadow: "0 6px 20px rgba(0,0,0,0.12)",
          filter: "brightness(1) saturate(1) blur(0px)",
          duration: settleDuration * 0.6, // First phase - quick settle
          ease: settleEase,
          zIndex: message.isAdmin ? 100 : 1, // Maintain admin priority
          onComplete: () => {
            // Second phase - gentle bounce back to final state
            gsap.to(messageRef.current, {
              scale: 1,
              rotation: rotationRef.current,
              duration: settleDuration * 0.4, // Second phase - gentle bounce
              ease: "power2.out",
              onComplete: () => {
                gsap.set(messageRef.current, {
                  clearProps: "willChange",
                  transformOrigin: "center center",
                  zIndex: message.isAdmin ? 100 : 1, // Maintain admin priority after animation
                });
              },
            });
          },
        });

        // Calculate the exact final position with pixel-perfect accuracy
        const newPosition = {
          x: Math.round(safePosition.x + finalX),
          y: Math.round(safePosition.y + finalY),
        };

        // Update position in database with debouncing
        updateMessagePosition(message.id, newPosition);
      },
      onThrowUpdate: function () {
        // Enhanced floating effect during momentum with speed-based animations
        if (this.isThrowing) {
          const velocity = Math.min(
            Math.abs(this.deltaX) + Math.abs(this.deltaY),
            80
          );
          const normalizedVelocity = velocity / 80; // 0 to 1

          // Dynamic effects during momentum phase
          const momentumScale = 1.02 + normalizedVelocity * 0.04; // 1.02 to 1.06
          const shadowBlur = 15 + velocity * 0.4;
          const shadowSpread = 25 + velocity * 0.6;
          const shadowIntensity = 0.15 + normalizedVelocity * 0.1;

          // Subtle rotation during momentum
          const momentumRotation = rotationRef.current + this.deltaX * 0.01;

          gsap.to(messageRef.current, {
            scale: momentumScale,
            rotation: momentumRotation,
            boxShadow: `0 ${shadowBlur}px ${shadowSpread}px rgba(0,0,0,${shadowIntensity})`,
            filter: `brightness(${1.02 + normalizedVelocity * 0.02}) saturate(${
              1.05 + normalizedVelocity * 0.05
            })`,
            duration: 0.1,
            ease: "none",
            overwrite: "auto",
          });
        }
      },
      onThrowComplete: function () {
        // Enhanced final settling after momentum ends with admin priority
        const finalX = this.x;
        const finalY = this.y;

        // Final settling animation with gentle bounce
        gsap.to(messageRef.current, {
          scale: 1,
          rotation: rotationRef.current,
          boxShadow: "0 6px 20px rgba(0,0,0,0.12)",
          filter: "brightness(1) saturate(1)",
          duration: 0.5,
          ease: "power3.out",
          onComplete: () => {
            gsap.set(messageRef.current, {
              clearProps: "willChange",
              transformOrigin: "center center",
              zIndex: message.isAdmin ? 100 : 1, // Maintain admin priority after momentum
            });
          },
        });

        const newPosition = {
          x: Math.round(safePosition.x + finalX),
          y: Math.round(safePosition.y + finalY),
        };

        updateMessagePosition(message.id, newPosition);
      },
    })[0];

    // Enhanced update method for position changes
    if (draggableRef.current) {
      draggableRef.current.update();
      // Ensure smooth transitions when position updates
      gsap.set(messageRef.current, {
        x: 0,
        y: 0,
        transformOrigin: "center center",
      });
    }

    return () => {
      if (draggableRef.current) {
        draggableRef.current.kill();
      }
      // Clean up any remaining tweens - capture ref value
      // eslint-disable-next-line react-hooks/exhaustive-deps
      const element = messageRef.current;
      if (element) {
        gsap.killTweensOf(element);
      }
    };
  }, [
    message.id,
    message.isAdmin,
    safePosition.x,
    safePosition.y,
    updateMessagePosition,
    boardId,
  ]);

  // Enhanced style optimized for smooth GSAP animations with admin priority
  const style = {
    position: "absolute" as const,
    left: safePosition.x,
    top: safePosition.y,
    transform: `rotate(${rotationRef.current}deg)`,
    // Admin cards always stay on top with higher z-index
    zIndex: isDragging
      ? 1000
      : message.isAdmin
      ? 100 // Admin cards always above regular cards
      : 1, // Regular cards at base level
    width: message.width || 200, // Compact fallback width matching pritambose.netlify.app
    touchAction: "none",
    cursor: isDragging ? "grabbing" : "grab",
    // Remove CSS transitions to let GSAP handle all animations smoothly
    willChange: isDragging ? "transform, box-shadow, filter" : "auto",
    // Base shadows - GSAP will override these during drag
    boxShadow: message.isAdmin
      ? "0 8px 25px rgba(244,63,94,0.15)" // Rose shadow for admin
      : "0 6px 20px rgba(0,0,0,0.1)",
    // Optimized for hardware acceleration
    backfaceVisibility: "hidden" as const,
    perspective: 1000,
    transformStyle: "preserve-3d" as const,
  };

  // Enhanced function to get background gradient class with brighter, more vibrant colors (reverted to original)
  const getBackgroundGradient = (gradientClass: string) => {
    const colorMap = {
      // Match the exact gradient formats from the updated colors array
      "from-emerald-500 to-emerald-700":
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
      "from-purple-500 to-indigo-700":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-rose-500 to-red-700":
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
      "from-cyan-500 to-teal-700":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
      "from-amber-500 to-orange-700":
        "bg-gradient-to-br from-amber-500/80 to-orange-700/80",

      // Legacy support for older colors (map to new vibrant colors)
      "from-emerald-800 to-emerald-900":
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
      "from-purple-800 to-purple-900":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-red-900 to-red-950":
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
      "from-teal-800 to-teal-900":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",

      // More legacy support
      "from-emerald-400 to-emerald-600":
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
      "from-violet-400 to-purple-600":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-blue-400 to-indigo-600":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-amber-400 to-orange-600":
        "bg-gradient-to-br from-amber-500/80 to-orange-700/80",
      "from-teal-400 to-cyan-600":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
      "from-purple-400 to-purple-600":
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
      "from-red-400 to-red-600":
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
      "from-teal-400 to-teal-600":
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
    };

    // If the gradient class exists in the map, use it; otherwise, pick a random color
    if (colorMap[gradientClass as keyof typeof colorMap]) {
      return colorMap[gradientClass as keyof typeof colorMap];
    } else {
      // For any unrecognized gradient, pick a random vibrant color
      const fallbackColors = [
        "bg-gradient-to-br from-emerald-500/80 to-emerald-700/80",
        "bg-gradient-to-br from-purple-500/80 to-indigo-700/80",
        "bg-gradient-to-br from-rose-500/80 to-red-700/80",
        "bg-gradient-to-br from-cyan-500/80 to-teal-700/80",
        "bg-gradient-to-br from-amber-500/80 to-orange-700/80",
      ];
      return fallbackColors[Math.floor(Math.random() * fallbackColors.length)];
    }
  };

  // Render mobile or desktop message based on width matching pritambose.netlify.app
  const isMobile = message.width && message.width < 170; // Compact threshold for pritambose.netlify.app style

  return (
    <div ref={messageRef} style={style} className="touch-none select-none">
      {isMobile ? (
        // Mobile message card with enhanced styling and admin distinction (reverted to original vibrant colors)
        <div
          className={`backdrop-blur-sm rounded-lg shadow-lg transition-all duration-200 ease-out
            ${
              message.isAdmin
                ? "bg-gradient-to-br from-rose-500/70 to-pink-600/70 border border-rose-400/30 ring-1 ring-rose-400/20 shadow-rose-500/20 shadow-xl"
                : getBackgroundGradient(message.gradient)
            }`}
          style={{
            width: `${message.width}px`,
            padding: message.width && message.width <= 150 ? "12px" : "16px", // Compact padding like pritambose.netlify.app
          }}
        >
          {/* Message content with improved typography */}
          <p className="text-white/95 break-words text-sm font-medium raleway-font leading-snug mb-3">
            {message.text}
          </p>

          {/* Footer with enhanced admin badge and full date/time for mobile */}
          <div className="flex justify-between items-center mt-2 pt-1 border-t border-white/10 gap-2">
            {message.isAdmin ? (
              <span className="text-[10px] text-white bg-rose-500/50 px-2 py-0.5 rounded-full border border-rose-400/30 font-medium custom flex-shrink-0">
                Admin
              </span>
            ) : (
              <span className="text-[10px] text-white/60 flex-shrink-0">
                User
              </span>
            )}
            <span className="text-[9px] sm:text-[10px] text-white/70 font-medium text-right">
              {formatDate(message.timestamp)}
            </span>
          </div>
        </div>
      ) : (
        // Enhanced desktop message card with dynamic width (reverted to original vibrant colors)
        <div
          className={`rounded-xl p-0.5 group transition-all duration-300 hover:shadow-xl raleway-font
            ${
              message.isAdmin
                ? "bg-gradient-to-br from-rose-500/80 to-pink-600/80 ring-2 ring-rose-400/30 shadow-rose-500/25 shadow-2xl"
                : getBackgroundGradient(message.gradient)
            }`}
          style={{ width: `${message.width}px` }}
        >
          <div
            className={`h-full w-full backdrop-blur-sm rounded-xl p-4 border border-white/10
            ${
              message.isAdmin
                ? "bg-gradient-to-br from-black/30 to-black/10"
                : "bg-gradient-to-br from-black/40 to-black/20"
            }`}
          >
            {/* Admin indicator at top if admin */}
            {message.isAdmin && (
              <div className="flex items-center mb-3 -mt-1">
                <span className="text-xs text-rose-300 bg-rose-500/20 px-2.5 py-0.5 rounded-full border border-rose-400/20 font-medium custom">
                  Admin
                </span>
              </div>
            )}

            {/* Message content with improved typography */}
            <p
              className={`text-white/95 font-medium text-base leading-relaxed mb-4 ${
                message.isAdmin ? "custom" : "raleway-font"
              }`}
            >
              {message.text}
            </p>

            {/* Timestamp with enhanced styling */}
            <div className="flex justify-end items-center">
              <div className="flex items-center space-x-1 text-xs text-white/70 bg-black/30 px-3 py-1 rounded-full border border-white/10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-white-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 10h.01M12 10h.01M16 10h.01M21 12c0 4.418-4.03 8-9 8a9.77 9.77 0 01-4-.84l-4.38 1.13a1 1 0 01-1.22-1.22l1.13-4.38A9.77 9.77 0 013 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>

                <span>{formatDate(message.timestamp)}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Board component for GSAP draggable - add margin bottom for laptop
const MessageBoard = ({
  children,
  id,
  boardRef,
}: {
  children: React.ReactNode;
  id: string;
  boardRef: React.RefObject<HTMLDivElement>;
}) => {
  return (
    <div
      id={id}
      ref={boardRef}
      className="relative bg-white/5 rounded-2xl lg:rounded-3xl p-4 lg:p-12 backdrop-blur-xl border border-[#45d6e9]/10 shadow-2xl shadow-[#45d6e9]/5 min-h-[60vh] sm:min-h-[65vh] lg:min-h-[600px] overflow-hidden touch-none mb-1 lg:mb-1"
    >
      <div className="absolute inset-0 bg-[linear-gradient(rgba(69,214,233,0.07)_1px,transparent_1px),linear-gradient(90deg,rgba(69,214,233,0.07)_1px,transparent_1px)] bg-[size:20px_20px] rounded-2xl lg:rounded-3xl" />
      {children}
    </div>
  );
};

export default function AnonymousWall() {
  const [messages, setMessages] = useState<WallMessage[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isAdmin, setIsAdmin] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [emojiPosition, setEmojiPosition] = useState({ top: 0, left: 0 });
  const [dbInitialized, setDbInitialized] = useState(false);
  const [dbError, setDbError] = useState<string | null>(null);
  const boardRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const dragPositionsRef = useRef<Map<string, { x: number; y: number }>>(
    new Map()
  );
  // Initialize with window dimensions instead of zeros
  const [viewportDimensions, setViewportDimensions] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 1024, // Default to desktop size
    height: typeof window !== "undefined" ? window.innerHeight : 768,
  });
  const [boardDimensions, setBoardDimensions] = useState({
    width: 0,
    height: 0,
    top: 0,
    left: 0,
  });

  // Card dimensions (state to track card sizes for different screen widths)
  const [cardDimensions, setCardDimensions] = useState({
    width: 250,
    height: 150, // Default values, will be updated based on screen size
  });

  // GSAP draggable is initialized in the DraggableMessage component

  // Emoji picker handlers
  const onEmojiClick = (emojiData: EmojiClickData) => {
    setNewMessage((prev) => prev + emojiData.emoji);
    setShowEmojiPicker(false);
  };

  const handleEmojiButtonClick = (event: React.MouseEvent) => {
    event.preventDefault();
    const rect = event.currentTarget.getBoundingClientRect();
    setEmojiPosition({ top: rect.bottom + 5, left: rect.left });
    setShowEmojiPicker((prev) => !prev);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (
        showEmojiPicker &&
        !target.closest('[data-emoji-picker="true"]') &&
        !target.closest(".emoji-button")
      ) {
        setShowEmojiPicker(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showEmojiPicker]);

  // Database initialization
  useEffect(() => {
    if (!DATABASE_ID || !COLLECTION_ID || !PROJECT_ID) {
      setDbError("Missing environment variables");
      return;
    }
    setDbInitialized(true);
    loadMessages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update board dimensions when board ref or viewport dimensions change
  useEffect(() => {
    if (boardRef.current) {
      const updateBoardDimensions = () => {
        const rect = boardRef.current?.getBoundingClientRect();
        if (rect) {
          setBoardDimensions({
            width: rect.width,
            height: rect.height,
            top: rect.top,
            left: rect.left,
          });
        }
      };

      // Initial update
      updateBoardDimensions();

      // Add resize observer for more accurate tracking
      const resizeObserver = new ResizeObserver(updateBoardDimensions);
      resizeObserver.observe(boardRef.current);

      return () => {
        if (boardRef.current) {
          // eslint-disable-next-line react-hooks/exhaustive-deps
          resizeObserver.unobserve(boardRef.current);
        }
      };
    }
  }, [boardRef, viewportDimensions]);

  // Add effect to update viewport dimensions on window resize
  useEffect(() => {
    // Function to update viewport dimensions
    const updateViewportDimensions = () => {
      setViewportDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Add event listener for resize
    window.addEventListener("resize", updateViewportDimensions);

    // Call once on mount to ensure correct initial dimensions
    updateViewportDimensions();

    // Clean up
    return () => window.removeEventListener("resize", updateViewportDimensions);
  }, []);

  // Exact card dimensions matching pritambose.netlify.app
  const MESSAGE_WIDTH =
    viewportDimensions.width <= 380
      ? 120 // Compact mobile cards
      : viewportDimensions.width <= 768
      ? 150 // Small tablet size
      : viewportDimensions.width <= 1024
      ? 180 // Medium tablet size
      : 200; // Compact desktop cards like pritambose.netlify.app
  const MESSAGE_HEIGHT =
    viewportDimensions.width <= 380
      ? 90 // Compact mobile height
      : viewportDimensions.width <= 768
      ? 110 // Small tablet height
      : viewportDimensions.width <= 1024
      ? 120 // Medium tablet height
      : 130; // Compact desktop height like pritambose.netlify.app

  // Update card dimensions based on screen size using the constants
  useEffect(() => {
    setCardDimensions({
      width: MESSAGE_WIDTH,
      height: MESSAGE_HEIGHT,
    });
  }, [viewportDimensions, MESSAGE_WIDTH, MESSAGE_HEIGHT]);

  // Stable repositioning - only when board dimensions are ready and cards are truly outside
  useEffect(() => {
    if (!dbInitialized) return;

    // Only reposition if board dimensions are actually available (not fallbacks)
    if (boardDimensions.width === 0 || boardDimensions.height === 0) return;

    const repositionMessages = () => {
      // Get current messages from state
      setMessages((currentMessages) => {
        if (currentMessages.length === 0) return currentMessages;

        // Get current card dimensions
        const cardWidth = cardDimensions.width;
        const cardHeight = cardDimensions.height;

        // Use actual board dimensions only (no fallbacks to prevent random positioning)
        const boardWidth = boardDimensions.width;
        const boardHeight = boardDimensions.height;

        const maxX = Math.max(10, boardWidth - cardWidth - 10); // Leave 10px margin
        const maxY = Math.max(10, boardHeight - cardHeight - 10); // Leave 10px margin

        // Only adjust messages that are actually outside boundaries
        let hasChanges = false;
        const adjustedMessages = currentMessages.map((message) => {
          // Get current position with fallbacks
          const currentX =
            typeof message.position.x === "number" ? message.position.x : 20;
          const currentY =
            typeof message.position.y === "number" ? message.position.y : 20;

          // Only reposition if significantly outside boundaries (with tolerance)
          const isOutsideX = currentX > maxX + 10 || currentX < 5;
          const isOutsideY = currentY > maxY + 10 || currentY < 5;

          if (isOutsideX || isOutsideY) {
            // Calculate new position within boundaries (preserve as much as possible)
            const newX = isOutsideX
              ? Math.max(10, Math.min(currentX, maxX))
              : currentX;
            const newY = isOutsideY
              ? Math.max(10, Math.min(currentY, maxY))
              : currentY;

            // Only update database if position actually changed
            if (newX !== currentX || newY !== currentY) {
              hasChanges = true;
              databases
                .updateDocument(DATABASE_ID, COLLECTION_ID, message.id, {
                  position: JSON.stringify({ x: newX, y: newY }),
                })
                .catch((error) => {
                  console.error("Error updating message position:", error);
                });
            }

            return {
              ...message,
              position: { x: newX, y: newY },
              width: cardWidth,
              height: cardHeight,
            };
          }

          // If message is within boundaries, just update dimensions without changing position
          return {
            ...message,
            width: cardWidth,
            height: cardHeight,
          };
        });

        // Only return updated messages if positions actually changed
        if (hasChanges) {
          return adjustedMessages;
        }
        return currentMessages;
      });
    };

    // Add a small delay to ensure board dimensions are stable
    const timeoutId = setTimeout(repositionMessages, 100);
    return () => clearTimeout(timeoutId);
  }, [
    boardDimensions.width,
    boardDimensions.height,
    cardDimensions.width,
    cardDimensions.height,
    dbInitialized,
  ]);

  const loadMessages = async () => {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID
      );

      // Use the MESSAGE_WIDTH and MESSAGE_HEIGHT constants for consistency
      const cardWidth = MESSAGE_WIDTH;
      const cardHeight = MESSAGE_HEIGHT;

      // Calculate safe boundaries (if board dimensions are available)
      const maxX =
        boardDimensions.width > 0
          ? Math.max(0, boardDimensions.width - cardWidth)
          : 500;
      const maxY =
        boardDimensions.height > 0
          ? Math.max(0, boardDimensions.height - cardHeight)
          : 500;

      const loadedMessages = response.documents.map((doc) => {
        // Parse the position from the database
        const originalPosition = JSON.parse(doc.position);

        // Ensure position is within boundaries
        const position = {
          x: Math.max(0, Math.min(originalPosition.x, maxX)),
          y: Math.max(0, Math.min(originalPosition.y, maxY)),
        };

        return {
          id: doc.$id,
          text: doc.text,
          timestamp: doc.timestamp,
          color: doc.color,
          border: doc.border,
          shadow: doc.shadow,
          gradient: doc.gradient,
          opacity: doc.opacity,
          position: position,
          isAdmin: doc.isAdmin || false,
          width: cardWidth,
          height: cardHeight,
        };
      });

      setMessages(loadedMessages);
    } catch (error) {
      console.error("Error loading messages:", error);
      setDbError("Database connection error");
    }
  };

  // Update message position in the database
  const handleMessageDragEnd = async (
    id: string,
    position: { x: number; y: number }
  ) => {
    // Find the message
    const message = messages.find((msg) => msg.id === id);
    if (!message) return;

    // Use actual card dimensions from state
    const cardWidth = cardDimensions.width;
    const cardHeight = cardDimensions.height;

    // Calculate boundaries based on current board dimensions with improved fallbacks
    const isSmallScreen = window.innerWidth <= 768;
    const boardWidth =
      boardDimensions.width > 0
        ? boardDimensions.width
        : window.innerWidth - (isSmallScreen ? 32 : 40); // Account for mobile padding
    const boardHeight =
      boardDimensions.height > 0
        ? boardDimensions.height
        : isSmallScreen
        ? Math.max(500, window.innerHeight - 300) // Better mobile calculation
        : window.innerHeight - 200;

    // Add margins to prevent sticking to edges
    const maxX = Math.max(10, boardWidth - cardWidth - 10);
    const maxY = Math.max(10, boardHeight - cardHeight - 10);

    // Ensure position stays within boundaries
    const newX = Math.max(10, Math.min(position.x, maxX));
    const newY = Math.max(10, Math.min(position.y, maxY));

    // Important: Update only the dragged message, not all messages
    const newMessages = messages.map((msg) => {
      if (msg.id === id) {
        return {
          ...msg,
          position: { x: newX, y: newY },
        };
      }
      return msg;
    });

    // Set messages once to avoid duplicate updates
    setMessages(newMessages);

    // Update in the database
    try {
      await databases.updateDocument(DATABASE_ID, COLLECTION_ID, id, {
        position: JSON.stringify({ x: newX, y: newY }),
      });
      // After successful update, remove from ref
      dragPositionsRef.current.delete(id);
    } catch (error) {
      console.error("Error updating message position:", error);
      // Position will remain in ref for potential retry
    }
  };

  // Update message position in the database
  const updateMessagePositionInDB = async (
    id: string,
    position: { x: number; y: number }
  ) => {
    if (!dbInitialized) return;

    // Store position in ref for resilience
    dragPositionsRef.current.set(id, position);

    // Update the message position
    handleMessageDragEnd(id, position);
  };

  // Password Modal
  const PasswordModal = () => {
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (password === ADMIN_PASSWORD) {
        setIsAdmin(true);
        setShowPasswordModal(false);
      } else {
        setError("Incorrect password");
      }
    };

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-black/80 p-6 rounded-xl border border-[#45d6e9]/20 w-[300px]">
          <form onSubmit={handleSubmit} className="space-y-4">
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter admin password"
              className="w-full bg-transparent border border-[#45d6e9]/20 rounded-lg p-2 text-white"
            />
            {error && <p className="text-red-500 text-sm">{error}</p>}
            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={() => setShowPasswordModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#45d6e9]/20 text-[#45d6e9] rounded-lg hover:bg-[#45d6e9]/30"
              >
                Login
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Message handling
  const addMessage = async () => {
    if (!newMessage.trim() || !dbInitialized) return;

    const randomColor = colors[Math.floor(Math.random() * colors.length)];

    // Use the MESSAGE_WIDTH and MESSAGE_HEIGHT constants for consistency
    const cardWidth = MESSAGE_WIDTH;
    const cardHeight = MESSAGE_HEIGHT;

    // Calculate safe boundaries for new messages with improved fallbacks
    const isSmallScreen = window.innerWidth <= 768;
    const boardWidth =
      boardDimensions.width > 0
        ? boardDimensions.width
        : window.innerWidth - (isSmallScreen ? 32 : 40); // Account for mobile padding
    const boardHeight =
      boardDimensions.height > 0
        ? boardDimensions.height
        : isSmallScreen
        ? Math.max(500, window.innerHeight - 300) // Better mobile calculation
        : window.innerHeight - 200;

    // Add margins to prevent sticking to edges
    const maxX = Math.max(20, boardWidth - cardWidth - 20);
    const maxY = Math.max(20, boardHeight - cardHeight - 20);

    // For small screens, position more carefully
    // isSmallScreen already declared above
    const position = isSmallScreen
      ? {
          // On small screens, position in a more controlled way
          x: 20 + Math.floor(Math.random() * (maxX - 40)),
          y: 20 + Math.floor(Math.random() * (maxY - 40)),
        }
      : {
          // On larger screens, more random positioning
          x: Math.min(Math.random() * maxX, maxX),
          y: Math.min(Math.random() * maxY, maxY),
        };

    try {
      // Use the bgGradient property for better visual consistency
      const messageData = {
        text: newMessage,
        timestamp: Date.now(),
        color: randomColor.gradient || "",
        border: randomColor.border || "",
        shadow: randomColor.shadow || "",
        gradient: randomColor.gradient || "",
        opacity: randomColor.opacity || "",
        position: JSON.stringify(position),
        isAdmin,
      };

      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTION_ID,
        ID.unique(),
        messageData
      );

      setMessages((prev) => [
        ...prev,
        {
          id: response.$id,
          ...messageData,
          position,
          width: cardWidth,
          height: cardHeight,
        },
      ]);
      setNewMessage("");
    } catch (error) {
      console.error("Error adding message:", error);
    }
  };

  const clearAllMessages = async () => {
    if (!isAdmin || !dbInitialized) return;
    try {
      const messages = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_ID
      );
      await Promise.all(
        messages.documents.map((doc) =>
          databases.deleteDocument(DATABASE_ID, COLLECTION_ID, doc.$id)
        )
      );
      setMessages([]);
    } catch (error) {
      console.error("Error clearing messages:", error);
    }
  };

  // Viewport handling
  useEffect(() => {
    const updateDimensions = () => {
      setViewportDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    updateDimensions();
    window.addEventListener("resize", updateDimensions);
    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  if (dbError) {
    return (
      <section className="min-h-screen pt-24 px-4 md:px-8 flex items-center justify-center animated-gradient-bg">
        <div className="bg-black/50 backdrop-blur-md p-6 rounded-xl border border-[#45d6e9]/20 max-w-md">
          <h2 className="text-[#45d6e9] text-xl mb-4">Configuration Error</h2>
          <p className="text-white mb-6">{dbError}</p>
          <p className="text-gray-400 text-sm">
            Please check your environment variables configuration.
          </p>
        </div>
      </section>
    );
  }

  return (
    <>
      <section
        id="mysteryboard"
        className="min-h-screen relative pt-6 px-4 md:px-8  overflow-hidden animated-gradient-bg"
      >
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-40 z-[1]" />
        <div className="absolute inset-0 bg-[radial-gradient(#45d6e9_1px,transparent_1px)] [background-size:15px_15px] opacity-[0.15] z-[1]" />
        <div className="absolute inset-0 bg-[conic-gradient(at_top_left,#45d6e9_10%,transparent_30%,transparent_70%,#45d6e9_90%)] opacity-[0.2] [background-size:20px_20px] scale-[1.2] mix-blend-overlay z-[1]" />

        {/* Mobile Layout */}
        <div className="lg:hidden w-full p-4 relative z-[2] mt-2 mb-12">
          <div className="text-center mb-8 mt-4">
            <h1 className="text-4xl font-bold text-[#45d6e9] mb-2 custom">
              Anonymous Wall
            </h1>
            <p className="text-gray-400 text-base">
              Got thoughts? Share them anonymously.{" "}
            </p>
          </div>
          {/* input field - fixed for small mobile screens */}
          <div className="mb-6">
            <div className="relative flex items-center bg-[#1a1a1a]/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
              <input
                ref={inputRef}
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Write something nice..."
                className="flex-1 min-w-0 bg-transparent px-2 sm:px-3 py-1.5 text-gray-200 placeholder-gray-500 focus:outline-none text-sm sm:text-base"
                onKeyDown={(e) => e.key === "Enter" && addMessage()}
              />
              <div className="flex items-center flex-shrink-0">
                <button
                  onClick={handleEmojiButtonClick}
                  className="emoji-button p-1 sm:p-1.5 text-[#45d6e9]"
                >
                  <Smile className="h-4 w-4 sm:h-5 sm:w-5" />
                </button>
                <button
                  onClick={addMessage}
                  className="p-1 sm:p-1.5 text-[#45d6e9]"
                >
                  <Send className="h-4 w-4 sm:h-5 sm:w-5" />
                </button>
              </div>
            </div>
          </div>
          {/* board */}
          <MessageBoard id="mobile-board" boardRef={boardRef}>
            {messages.map((message) => (
              <DraggableMessage
                key={message.id}
                message={message}
                updateMessagePosition={updateMessagePositionInDB}
                boardId="mobile-board"
              />
            ))}
          </MessageBoard>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block max-w-7xl mx-auto w-full p-6 relative z-[2] mb-20">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-[#45d6e9] mb-2 custom">
              Anonymous Wall
            </h1>
            <p className="text-gray-400 text-lg">
              Got thoughts? Share them anonymously.{" "}
            </p>
          </div>
          {/* Responsive input field with proper mobile handling */}
          <div className="mb-8 flex justify-center px-4 sm:px-6 md:px-8">
            <div className="relative flex items-center gap-1 sm:gap-2 md:gap-3 bg-transparent backdrop-blur-sm border border-[#45d6e9]/20 rounded-2xl p-1 sm:p-1.5 md:p-2 w-full max-w-[800px] overflow-hidden">
              {/* Admin badge - only visible on larger screens */}
              {isAdmin && (
                <div className="hidden sm:flex absolute -top-3 -right-3 bg-rose-500/80 text-white text-[10px] px-2 py-0.5 rounded-full shadow-lg">
                  Admin
                </div>
              )}

              <input
                ref={inputRef}
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Write something nice..."
                className="flex-1 bg-transparent px-1 sm:px-2 md:px-4 py-2 text-gray-200 placeholder-gray-500 focus:outline-none text-sm sm:text-base md:text-lg min-w-0"
                onKeyDown={(e) => e.key === "Enter" && addMessage()}
              />

              {/* Send button - always visible */}
              <button
                onClick={addMessage}
                className="flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-[#45d6e9] hover:bg-[#45d6e9]/10 rounded-xl transition-all duration-300"
                aria-label="Send message"
              >
                <Send className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>

              {/* Admin controls - only visible on larger screens */}
              {isAdmin ? (
                <>
                  <button
                    onClick={() => setIsAdmin(false)}
                    className="hidden sm:block flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-rose-400 hover:bg-rose-400/10 rounded-xl"
                    aria-label="Exit admin mode"
                  >
                    <Lock className="h-4 w-4 sm:h-5 sm:w-5" />
                  </button>
                  <button
                    onClick={clearAllMessages}
                    className="hidden sm:block flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-rose-400 hover:bg-rose-400/10 rounded-xl"
                    aria-label="Clear all messages"
                  >
                    <Trash2 className="h-4 w-4 sm:h-5 sm:w-5" />
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setShowPasswordModal(true)}
                  className="hidden sm:block flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-[#45d6e9] hover:bg-[#45d6e9]/10 rounded-xl"
                  aria-label="Admin login"
                >
                  <Lock className="h-4 w-4 sm:h-5 sm:w-5" />
                </button>
              )}

              {/* Emoji button - always visible */}
              <button
                onClick={handleEmojiButtonClick}
                className="emoji-button flex-shrink-0 p-1 sm:p-1.5 md:p-2 text-[#45d6e9] hover:bg-[#45d6e9]/10 rounded-xl"
                aria-label="Add emoji"
              >
                <Smile className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            </div>
          </div>
          {/* board */}
          <MessageBoard id="desktop-board" boardRef={boardRef}>
            {messages.map((message) => (
              <DraggableMessage
                key={message.id}
                message={message}
                updateMessagePosition={updateMessagePositionInDB}
                boardId="desktop-board"
              />
            ))}
          </MessageBoard>
        </div>

        {/* Emoji Picker */}
        {showEmojiPicker && (
          <div
            data-emoji-picker="true"
            className="fixed z-[99999]"
            style={{
              top: emojiPosition.top,
              left:
                viewportDimensions.width <= 480 ? "50%" : emojiPosition.left,
              transform:
                viewportDimensions.width <= 480 ? "translateX(-50%)" : "none",
            }}
          >
            <EmojiPicker
              onEmojiClick={onEmojiClick}
              autoFocusSearch={false}
              width={viewportDimensions.width <= 480 ? 300 : 320}
              height={300}
            />
          </div>
        )}

        {/* Password Modal */}
        {showPasswordModal && <PasswordModal />}
      </section>
    </>
  );
}
